# 🔥 Configuration Firebase et Authentification - PiKnowKyo

## 📋 Vue d'ensemble

Ce document explique la nouvelle implémentation de l'authentification Firebase et de la synchronisation des scripts dans l'application PiKnowKyo.

## 🚀 Fonctionnalités implémentées

### ✅ Authentification complète
- **Connexion/Inscription** avec email/mot de passe
- **Connexion Google** (OAuth)
- **Connexion anonyme** (mode invité)
- **Gestion des sessions** automatique
- **Période d'essai premium** de 14 jours pour les nouveaux utilisateurs

### ✅ Système d'abonnements
- **Forfait gratuit** : accès limité
- **Forfait premium** : accès complet
- **Période d'essai** : 14 jours premium gratuit
- **Transition automatique** vers le forfait gratuit après l'essai

### ✅ Synchronisation des scripts
- **Upload automatique** des scripts JSON vers Firestore
- **Synchronisation en temps réel** selon la langue
- **Fallback intelligent** vers les fichiers locaux
- **Support multilingue** (français, anglais, espagnol)

## 🛠️ Configuration initiale

### 1. Firebase est déjà configuré
Le projet utilise la configuration Firebase existante :
- **Projet** : `piknowkyo-777`
- **Base de données** : Firestore
- **Authentification** : Email/Password + Google

### 2. Structure Firestore

#### Collection `users`
```javascript
{
  uid: string,
  subscription: {
    plan: 'free' | 'premium',
    status: 'active' | 'expired' | 'trial',
    startDate: Timestamp,
    trialEndDate?: Timestamp,
    isTrialActive: boolean
  },
  preferences: {
    language: string,
    theme: string,
    notifications: boolean
  },
  createdAt: Timestamp,
  lastLoginAt: Timestamp
}
```

#### Collection `scripts`
```javascript
{
  id: string,
  language: 'fr' | 'en' | 'es',
  title: string,
  description: string,
  type: string,
  estimatedDuration: number,
  tags: string[],
  isPremium: boolean,
  imageUrl?: string,
  script?: any[],
  audio?: any,
  benefits?: string[],
  comments?: any[],
  version: number,
  createdAt: Timestamp,
  updatedAt: Timestamp
}
```

## 📤 Upload des scripts vers Firestore

### Commande rapide
```bash
# Upload tous les scripts (ignore les existants)
npm run upload:scripts

# Force l'upload (remplace les existants)
npm run upload:scripts:force
```

### Script manuel
```bash
node scripts/upload-scripts-to-firestore.js
```

Le script :
1. **Lit les manifestes** pour chaque langue (fr, en, es)
2. **Charge les scripts complets** depuis `/public/assets/sessionScripts/`
3. **Upload vers Firestore** avec métadonnées complètes
4. **Évite les doublons** (sauf avec `--force`)

## 🔄 Fonctionnement de la synchronisation

### Priorité de chargement
1. **Firestore** (temps réel, toujours à jour)
2. **Fichiers locaux** (fallback si Firestore indisponible)

### Avantages
- ✅ **Mise à jour instantanée** des scripts
- ✅ **Pas de redéploiement** nécessaire pour ajouter du contenu
- ✅ **Synchronisation multilingue** automatique
- ✅ **Mode hors-ligne** avec fallback local

## 👤 Flux d'authentification

### Nouvel utilisateur
1. **Ouverture de l'app** → Modal d'authentification
2. **Inscription** → Création automatique du profil
3. **Période d'essai** → 14 jours premium gratuit
4. **Après 14 jours** → Transition automatique vers gratuit

### Utilisateur existant
1. **Connexion** → Vérification du statut d'abonnement
2. **Mise à jour automatique** si l'essai a expiré
3. **Accès selon le forfait** (gratuit/premium)

## 🎯 Utilisation dans le code

### Hook d'authentification
```typescript
import { useAuth } from './contexts/AuthContext';

const MyComponent = () => {
  const { 
    user, 
    userProfile, 
    isAuthenticated, 
    hasActivePremium, 
    getRemainingTrialDays,
    signIn,
    signUp,
    signOut 
  } = useAuth();

  // Vérifier si l'utilisateur a accès premium
  if (hasActivePremium()) {
    // Fonctionnalités premium
  }

  // Afficher les jours d'essai restants
  const trialDays = getRemainingTrialDays();
};
```

### Chargement des scripts
```typescript
import { fetchSessionManifest, fetchSessionWithScript } from './data/sessions';

// Charge automatiquement depuis Firestore puis fallback local
const manifest = await fetchSessionManifest('fr');
const session = await fetchSessionWithScript('session-id', 'fr');
```

## 🔧 Maintenance

### Ajouter de nouveaux scripts
1. **Ajouter les fichiers JSON** dans `/public/assets/sessionScripts/`
2. **Mettre à jour les manifestes** avec `npm run build:manifests`
3. **Uploader vers Firestore** avec `npm run upload:scripts`

### Modifier des scripts existants
1. **Modifier les fichiers JSON** locaux
2. **Re-uploader** avec `npm run upload:scripts:force`

### Surveillance
- **Console Firebase** pour voir les données Firestore
- **Logs de l'application** pour le debug de synchronisation
- **Métriques d'authentification** dans Firebase Console

## 🚨 Points importants

### Sécurité
- ✅ **Règles Firestore** à configurer pour la sécurité
- ✅ **Validation côté client** et serveur
- ✅ **Gestion des erreurs** robuste

### Performance
- ✅ **Cache local** avec fallback
- ✅ **Chargement à la demande** des scripts
- ✅ **Optimisation des requêtes** Firestore

### Évolutivité
- ✅ **Structure modulaire** facile à étendre
- ✅ **Support multilingue** natif
- ✅ **Système d'abonnements** flexible

## 📞 Support

Pour toute question ou problème :
1. **Vérifier les logs** de la console
2. **Consulter Firebase Console** pour les données
3. **Tester le fallback** local en cas de problème Firestore

---

🎉 **L'authentification Firebase et la synchronisation des scripts sont maintenant entièrement opérationnelles !**
