import React, { useState } from 'react';
import styled from 'styled-components';
import { useAuth } from '../contexts/AuthContext';
import AuthModal from './AuthModal';
import { useTranslation } from 'react-i18next';

const UserProfile: React.FC = () => {
  const { t } = useTranslation();
  const { user: currentUser, userProfile, signOut, isAuthenticated, hasActivePremium, getRemainingTrialDays } = useAuth();
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);

  const handleSignOut = async () => {
    try {
      await signOut();
      setShowUserMenu(false);
    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error);
    }
  };

  const getDisplayName = () => {
    if (userProfile?.displayName) {
      return userProfile.displayName;
    }
    if (currentUser?.displayName) {
      return currentUser.displayName;
    }
    if (currentUser?.email) {
      return currentUser.email.split('@')[0];
    }
    if (currentUser?.isAnonymous) {
      return t('auth.guest');
    }
    return t('auth.user');
  };

  const getSubscriptionBadge = () => {
    if (!userProfile) return null;

    const { plan, status, isTrialActive } = userProfile.subscription;

    if (status === 'expired') return null;

    const badgeColor = {
      free: '#95a5a6',
      premium: '#f39c12',
      pro: '#9b59b6'
    }[plan];

    let badgeText = t(`subscription.${plan}`, plan);

    if (isTrialActive) {
      const remainingDays = getRemainingTrialDays();
      badgeText = `${t('subscription.trial', 'Essai')} (${remainingDays}j)`;
    }

    return (
      <SubscriptionBadge color={badgeColor}>
        {badgeText}
      </SubscriptionBadge>
    );
  };

  if (!isAuthenticated) {
    return (
      <>
        <LoginButton onClick={() => setShowAuthModal(true)}>
          <UserIcon>👤</UserIcon>
          {t('auth.signIn')}
        </LoginButton>
        
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
          initialMode="login"
        />
      </>
    );
  }

  return (
    <UserContainer>
      <UserButton onClick={() => setShowUserMenu(!showUserMenu)}>
        <UserAvatar>
          {currentUser?.photoURL ? (
            <AvatarImage src={currentUser.photoURL} alt="Avatar" />
          ) : (
            <AvatarIcon>
              {currentUser?.isAnonymous ? '👤' : getDisplayName().charAt(0).toUpperCase()}
            </AvatarIcon>
          )}
        </UserAvatar>
        
        <UserInfo>
          <UserName>{getDisplayName()}</UserName>
          {getSubscriptionBadge()}
        </UserInfo>
        
        <DropdownIcon isOpen={showUserMenu}>▼</DropdownIcon>
      </UserButton>

      {showUserMenu && (
        <UserMenu>
          <MenuItem>
            <MenuIcon>📊</MenuIcon>
            {t('profile.stats')}
          </MenuItem>
          
          <MenuItem>
            <MenuIcon>⚙️</MenuIcon>
            {t('profile.settings')}
          </MenuItem>
          
          {userProfile?.subscription.plan === 'free' && (
            <MenuItem>
              <MenuIcon>⭐</MenuIcon>
              {t('profile.upgrade')}
            </MenuItem>
          )}
          
          <MenuDivider />
          
          <MenuItem onClick={handleSignOut}>
            <MenuIcon>🚪</MenuIcon>
            {t('auth.signOut')}
          </MenuItem>
        </UserMenu>
      )}
    </UserContainer>
  );
};

const UserContainer = styled.div`
  position: relative;
`;

const LoginButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: ${props => props.theme.primary};
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${props => props.theme.primaryDark};
    transform: translateY(-1px);
  }
`;

const UserIcon = styled.span`
  font-size: 1.1rem;
`;

const UserButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  background: ${props => props.theme.surface};
  border: 1px solid ${props => props.theme.border};
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${props => props.theme.surfaceAlt};
    transform: translateY(-1px);
  }
`;

const UserAvatar = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: ${props => props.theme.primary};
`;

const AvatarImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
`;

const AvatarIcon = styled.div`
  color: white;
  font-weight: bold;
  font-size: 0.9rem;
`;

const UserInfo = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 0.25rem;
`;

const UserName = styled.span`
  font-size: 0.9rem;
  font-weight: 500;
  color: ${props => props.theme.text};
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const SubscriptionBadge = styled.span<{ color: string }>`
  font-size: 0.7rem;
  padding: 0.1rem 0.4rem;
  background: ${props => props.color};
  color: white;
  border-radius: 10px;
  text-transform: uppercase;
  font-weight: bold;
`;

const DropdownIcon = styled.span<{ isOpen: boolean }>`
  font-size: 0.7rem;
  color: ${props => props.theme.textSecondary};
  transition: transform 0.2s ease;
  transform: ${props => props.isOpen ? 'rotate(180deg)' : 'rotate(0deg)'};
`;

const UserMenu = styled.div`
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  background: ${props => props.theme.surface};
  border: 1px solid ${props => props.theme.border};
  border-radius: 12px;
  box-shadow: ${props => props.theme.cardShadow};
  min-width: 200px;
  z-index: 1000;
  overflow: hidden;
`;

const MenuItem = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  color: ${props => props.theme.text};
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: ${props => props.theme.surfaceAlt};
  }
`;

const MenuIcon = styled.span`
  font-size: 1rem;
`;

const MenuDivider = styled.div`
  height: 1px;
  background: ${props => props.theme.border};
  margin: 0.5rem 0;
`;

export default UserProfile;
